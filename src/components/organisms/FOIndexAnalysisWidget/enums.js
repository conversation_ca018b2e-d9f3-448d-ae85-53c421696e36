import dayjs from 'dayjs';
import { isDarkMode } from '../../../utils/commonUtil';
import { ICONS_NAME } from '../../molecules/Icon';
import { isPostSession } from '../LightViewCandle/utils';

export const CHART_TYPES = {
  CANDLE: {
    icon: ICONS_NAME.CANDLE_CHART,
    key: 'Candle',
    label: 'Candle Charts',
  },
  LINE: {
    icon: ICONS_NAME.LINE_CHART,
    key: 'Line',
    label: 'Line Charts',
  },
};

export const PERIOD_TYPES = [
  {
    key: 'DAILY',
    label: 'Daily',
  },
  {
    key: 'MONTHLY',
    label: 'Monthly',
  },
  {
    key: 'YEARLY',
    label: 'Yearly',
  },
];

export const CHART_CONFIG = {
  layout: {
    attributionLogo: false,
    background: { color: 'transparent' },
    textColor: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
    fontSize: 10,
  },
  rightPriceScale: {
    visible: true,
    borderVisible: true,
    borderColor: isDarkMode() ? '#EEEEEE3D' : '#10101021',
    autoScale: true,
    mode: 0,
    alignLabels: true,
    priceFormat: {
      type: 'price',
      precision: 2,
      minMove: 0.01,
    },
  },
  timeScale: {
    visible: true,
    borderVisible: true,
    timeVisible: true,
    secondsVisible: false,
    allowBoldLabels: false,
    borderColor: isDarkMode() ? '#EEEEEE3D' : '#10101021',
    tickMarkFormatter: (function () {
      let firstTick = true;
      return (time) => {
        const date = dayjs(time * 1000);
        const now = dayjs();
        const isPreMarket =
          now.hour() < 9 || (now.hour() === 9 && now.minute() < 15);
        const compareDay = isPreMarket ? now.subtract(1, 'day') : now;

        if (isPreMarket && firstTick) {
          firstTick = false;
          // Show date and time for the first tick
          return date.format('DD MMM');
        }

        if (date.isSame(compareDay, 'day')) {
          return date.format('HH:mm');
        }
        return date.format('DD MMM');
      };
    })(),
    leftOffset: 12,
    rightOffset: 12,
    fixLeftEdge: true,
    fixRightEdge: isPostSession(dayjs()),
  },
  grid: {
    vertLines: {
      visible: false,
    },
    horzLines: {
      visible: false,
    },
  },
  crosshair: {
    mode: 2,
    vertLine: {
      visible: false,
      labelVisible: false,
    },
    horzLine: {
      visible: false,
      labelVisible: false,
    },
  },
  handleScroll: {
    mouseWheel: false,
    pressedMouseMove: false,
    horzTouchDrag: false,
    vertTouchDrag: false,
  },
};

export const TAB_TYPES = {
  CHANGE_IN_OI: 'change-in-oi',
  OPEN_INTEREST: 'open-interest',
  PRICE_CHART: 'price-chart',
  FII_DII: 'fii-dii',
  ATM_STRADDLE: 'atm-straddle',
  PCR: 'pcr',
  ADVANCE_DECLINE: 'advance-decline',
  MAX_PAIN: 'max-pain',
};

export const BAR_CHART_OPTIONS = (yaxisTickFormatter) => ({
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: 'index',
    intersect: false,
  },
  layout: {
    padding: {
      left: 15,
    },
  },
  plugins: {
    legend: {
      display: false,
      position: 'top',
      labels: {
        usePointStyle: true,
        pointStyle: 'circle',
        color: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
        font: {
          size: 12,
          weight: '500',
        },
        generateLabels: (chart) => {
          const { datasets } = chart.data;
          return datasets.map((dataset, i) => ({
            text: dataset.label,
            fillStyle: dataset.backgroundColor,
            strokeStyle: dataset.borderColor,
            lineWidth: dataset.borderWidth,
            pointStyle: 'circle',
            datasetIndex: i,
          }));
        },
      },
    },
    tooltip: {
      enabled: false,
    },
  },
  scales: {
    x: {
      title: {
        display: false,
      },
      ticks: {
        color: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
        font: {
          size: 10,
        },
      },
      offset: true,
      grid: {
        display: false,
      },
      border: {
        display: true,
        color: isDarkMode() ? '#EEEEEE3D' : '#10101021',
      },
    },
    y: {
      type: 'linear',
      position: 'right',
      title: {
        display: false,
      },
      offset: false,
      grid: {
        display: true,
        color: isDarkMode() ? '#EEEEEE1F' : '#1010100F',
      },
      ticks: {
        color: isDarkMode() ? '#EEEEEE8A' : '#1010108A',
        font: {
          size: 10,
        },
        padding: 10,
        callback: (value) => yaxisTickFormatter(value),
      },
      border: {
        display: true,
        color: isDarkMode() ? '#EEEEEE3D' : '#10101021',
      },
    },
  },
});
